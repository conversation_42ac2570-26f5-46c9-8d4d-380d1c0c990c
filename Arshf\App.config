﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System">
      <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  
      <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
          <section name="Arshf.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
      </sectionGroup>
  </configSections>

  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
<connectionStrings><add name="DBAREntities" connectionString="metadata=res://*/DBAR.csdl|res://*/DBAR.ssdl|res://*/DBAR.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=.\SQLEXfPRESS;initial catalog=DBAR;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" /></connectionStrings><userSettings>
        <Arshf.Properties.Settings>
            <setting name="FullName" serializeAs="String">
                <value />
            </setting>
            <setting name="UserName" serializeAs="String">
                <value />
            </setting>
            <setting name="UserDep" serializeAs="String">
                <value />
            </setting>
            <setting name="UserRole" serializeAs="String">
                <value />
            </setting>
            <setting name="CompanyName" serializeAs="String">
                <value>اسم المؤسسة</value>
            </setting>
            <setting name="CompanyDes" serializeAs="String">
                <value>وصف المؤسسة</value>
            </setting>
            <setting name="CompanyLogo" serializeAs="String">
                <value />
            </setting>
        </Arshf.Properties.Settings>
    </userSettings>
</configuration>