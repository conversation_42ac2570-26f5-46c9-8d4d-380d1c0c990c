﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btn_add.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAAhdEVYdFRpdGxlAEFwcGx5O09LO0NoZWNrO0JhcnM7
        UmliYm9uO2RjyGgAAAFKSURBVFhHxdAxTgNBEARA2wEpiH9hJETCI0j5Ck8gxI+AABAPgoCj27q1eoa+
        27XkW4KS3KOd65FXwzD8KzvsyQ57ssOe7LCnEB6eHhch399oH4WQF09l/PYVvMKFdh5+UFlYAMu/YIAP
        OBzR4wAtL/hPrHsc4MqZr0vnkgdUy2mpA5rKKQQ8OIXmcgoBj9Q65RZHlVMIeFjcwzOcyazm6HIKAY+J
        5T/AD+yg5YjJcggdWQh4fAelvKgdMVtO2pGFgMfn8A76MZo6olpO2pGFMC60HtFUTtqRhSBLtSOay0k7
        shDS4tQRL9BcTtqRhWCWp45QLN+C29/TjiwEtwxzR1TLSTuyENzyiEd8gpZ/ww2494F2ZCG4ZXEJ5QiW
        34J794d2ZCG45YRHvEFzOWlHFoJbNjZmNks7MjvsyQ57ssOe7LAnO+xnWP0CbFjkt+hdVzwAAAAASUVO
        RK5CYII=
</value>
  </data>
  <metadata name="toastNotificationsManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="toastNotificationsManager1.Notifications" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAAhdEVYdFRpdGxlAEFwcGx5O09LO0NoZWNrO0JhcnM7
        UmliYm9uO2RjyGgAAAFKSURBVFhHxdAxTgNBEARA2wEpiH9hJETCI0j5Ck8gxI+AABAPgoCj27q1eoa+
        27XkW4KS3KOd65FXwzD8KzvsyQ57ssOe7LCnEB6eHhch399oH4WQF09l/PYVvMKFdh5+UFlYAMu/YIAP
        OBzR4wAtL/hPrHsc4MqZr0vnkgdUy2mpA5rKKQQ8OIXmcgoBj9Q65RZHlVMIeFjcwzOcyazm6HIKAY+J
        5T/AD+yg5YjJcggdWQh4fAelvKgdMVtO2pGFgMfn8A76MZo6olpO2pGFMC60HtFUTtqRhSBLtSOay0k7
        shDS4tQRL9BcTtqRhWCWp45QLN+C29/TjiwEtwxzR1TLSTuyENzyiEd8gpZ/ww2494F2ZCG4ZXEJ5QiW
        34J794d2ZCG45YRHvEFzOWlHFoJbNjZmNks7MjvsyQ57ssOe7LAnO+xnWP0CbFjkt+hdVzwAAAAASUVO
        RK5CYII=
</value>
  </data>
  <data name="pictureBox1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAABGdBTUEAALGPC/xhBQAAGkRJREFUeF7t
        nQl0FMeZx4k3xyab3ezbfYkTG00fM0LSdI8kdEsInSCEJC5xmFvcpzD3EYjxBbGxgRgbc9gONwbMYWIM
        NrE5AuawMSZgnMRXbMex4yR2srl215jl2/pqqkV3qyQkGI00M9/vvf+T3hzdVV/9a7qr62pHEARBEARB
        EARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARB
        EARBEG2L9PT0r8TpZoaq+0cqmrlU0c2nFdU4wfSOohl/cukjpreZXmXvH/Lo/tUe1ZyqqkY3pu+KQxJE
        ZMPMnMoqw3xm9BeZ/s4EIdAVVrnOsr+LVDUph53mS8GzEUQEoCjJGjPvPUwfCENz6boBOSlp0LcgDyb3
        KIKFA8vgoZGVsKm2Nzx5ex+HNkzuDY9P6AGPjKmCxUPLYUrPYhhQ1AkK0jNA0017ZUH9Mnh1Sf13kQSC
        aHuoaqLq0Y31zLBfWObtaHaE4V3z4T5m8p3T+8LeWf1uWDumVcO9g7vBoNJ8MBNT7BXlH0zLvN7k74gk
        EUTro2nmzaydsIqZ8xIaVfea0K8wD5bWVMDTEoOHUrtn9IU72ZWoS3YmqFcryt9V3bj/24bxTZFEgmgd
        WFugmlWOT9GYXl8ARpQV8NsjmZlbWng7VpGXbb+ifIjpE0kliPCBv84ezdhgmbFnpxxYP6mX1Ljh1opR
        lVCQll5XUVg6t+t6+rdE0gmiZWnf3vgPZrzTaL74ePPKggFdpUZtTT09sy/M7FMKHeIDV0RF+bV44kUQ
        LQe2N9i9/gU0XbI/ld/WyAzaVvTExJ72q8kl1jaZx7JxUzA3BBFC2rfP/TqrHK+g2fBxbWu1NZorbMhj
        2+hqI958ASu6yBZBhAbWGN9qXTkipXLYhY+ajcRk62ryO8VrlIisEcSNoar+XmgsfFLV1m+rGtPGyb34
        I2FRSS4zLbvllvRviGwSRLO5yaOZQ5mRPkFTTetdIjVeJGnPzH4wobLQ3iP/tqKblSyvNGSFaDrsPr2M
        meecMBEUZmRwc8lMF4laNbYH5He8+jiY6Vce1bzd4zH8Pp/vayIMBOHEMIyvMrOstIyTaqTC/P5do6py
        WNrN8jSnbxc+JMbKb51U45AICUEE8XrNOGYO3seBQ0ZqexTDrhmhGUPVloVPurA/p0/nXHtl+ZsIC0G0
        a6friR2YKXAeBqSZqbBidOQ2xm9Ea8f35BXEoxlvidAQsU57r+FTdOO3aIyuWVmwfWq11DyxILySYBzY
        LdZuER4ilomLS7iF/Vr+Bk1Rlp0VsmHpkaryXGvAozlahIiIYW7CnmU0BPYPxHrlWDmmCtTg49+/3npr
        4n+KGBGxCqscs7FymInJsHlK5PWOh1I7p1dDbkpasP2hGgtFiIhYxePzG8wMn+MYpfuHdZeaJla0jbW5
        SjJFD7tqvEOTrQh29TD2oSEGl+ZLTdOY0EzYcfjUtOi4JaurHJrx+/Z6ICBCRMQqmpZUgIboEB+ALVP6
        SE3TkHBRBWEm6N05p8Wn1YZDth71ZSJERCzDbquOoSEmVhVJDdOYlo2oqKsgqNHlhRFfSR4aVQmazvNz
        yeMNpIswEbEI7/PQjCsdfAHYwRqmMsM0ppHdOvOKUVFWCV5fcPj4bcV5fNiG7PORopquwXwpmvk6jcWK
        YTyq/040Qv+iPKlRGhNWghR/KjfSkb1Pwq51q8DXIbj0DnYw/nhi25iXfj3C5YRwBEGwkhiLRLiIWAOH
        UKAJHqhp/pMrXOwNv1tcWAZ/+MUprgPb1kN6eh5/PZ61aab0KIY9MyOz8b6spsLqB/lCUZLSRMiIWIE1
        zhU0sj8xmS9sIDNJY8oX87tX/PDeugqCeuvUCzD0tkH8PRSOAMZFE1q74xEHIc7pWwoZgY78SVVTBl7i
        QnfBfJinWMhojkgsoarmbVj4lXnZUnM0piXDu3PjJKdkw/uvHXVUEEu71q+B0uLgVQYVSEqBMeUFsHpc
        D+kxW0p4u4QVAyuqlRbUxMprP5TYPq0PJCUE21Ye3awSoSNiAUU1lmPB49q4MnM0prKcLG6axT+YL60c
        lj65eAI2rXoIurNGvGVMVG5qGoyvKORPwVpiCP2Gyb34vJXqgjz++LruvLmFsGjB90HzmuDzmXylE9n3
        7cJ04nfZ7ehBEToiFlB04zAW/P3saiAzRkN6ZHQVN0ySPx3eeeWQtGLI9OLuLTBh5GjwGxl1hkWhUUuz
        MmFol3yY3ruUL6jw2PiefM44/vpb58XbQOx32TqlD38AsIpdiR5maVk8pJzfwo1iV6fuudn8SmU/Pqp3
        jz6wefUK+JhVWEzLtIkTg68X5DryJhOe08sqFPv8ZRqTFUNYa1qh0WTGaEi98nO5uX4wa0a9StAUfXT+
        OOzd9BhMnzSJN/A179Vf+FApJTUbhg8eCmuW3g/nj+yrl4Y3jj8H8QmpfJX5pjxts66YeFsqwkdEO9ac
        j+Ys27OWVSZ8soPm+sVLz9Uz3vXovVcPw76tP4aHfngPzL59CgzuPwiKCrpAZmanelcbw8yAQCATsrM6
        Q15eEXQtLYeaIUNh/ozpsHLJYtizcS28+sLT8Ps3TkrPZdesKbX8mNgQl+XVLmyvBNPgf1CEj4h2WIH/
        NxZ6c54uYX8Jfgd//WWmiySdem4Xzwu2Uey3cjItYrdxvILoxgERPiLaYQWOa0E1uZ8CF6TW2dUD56e/
        dvgZqekiTdbDgwUDyqR5toSrnvAKohnnRfiIaIcVNt8GDYd3y0zh1viK4G3G2BEjpWaLROFtGeYJG/ey
        PFvC+TH4OaZPRPiIaIcVNt/LoyltEFzmJ8UffDp0eM8WqdkiUW+ffoEPj8GF47bc3nAccJyaqCB/FeEj
        oh2cDISFjvv/yUxhFz5Kxc/iLYnMaJGsAX0H8LzN79dFmncU3oaKCnJJhI+IdlhhH8dCx+3RZKawqyov
        hxvk8YcelJoskoVDZTBvjfWJ4DAV/AzT/4jwEdGOqplPYaHP69f4pjfYYYf9Bfho99dnDktNFsnCx8IY
        BxxS0tCKkbaJYb8T4SOiHVbYi7DQx3YvlJrC0tRexdwco4bWSA0WDeqUG3wA8aNRldIYYDsN32f6pQgf
        Ee2Ildr5noIyU1iy5mhvf3yl1Fwn9j/Fx1vdOXc2DBs0FMq7VkBB5xLIysjnnXqW0tLyoBt7b9jAwTBv
        2lRYt2Ip/y6O15IdV6bfvX6Sf+fHDz8Ic6fezkcNl3Xpzo9tPxeeG9OAacE0Ydowjfhd2XGnT57E84jD
        82UxeJhVnGAF4aN6iVggzptkYqHj2CWZKVD4eBOnn3rjU+D9s0ek5sKe7KB5rk9o6Jm1tdJj2zVj8mQw
        TWfPenOFaZUde8PK5fz9nvnyHwvcXhrf92jGXhE+Iga4iRX6f2HBNzSqFTvQ8P1+vfpJjYU6/fwu0H11
        +2vwIfA4DKS0pBv/BbeEr3VIdA45t/T0prXSY9uFw0hk301I7MiPbT8Xnhtfw7RYn8M0Ylplx8aOT/wM
        PsqWxQHn64vj3CNiR8QCimrux4Kf28AjzkElnbgxlt17l9RYlraueZib/OKxa4/PwglV+7Y8wYedo5lr
        x46Xfk6myePG8UfNOMwej/HW6Z9KP2fX68cO8MqFaZS9b6ljx+AgTFm/UBW7DcX32BWknwgdEQsoujkK
        C75bTlY9U6CykoPL/+NQdZmpoknYVsG83j24W7044F6M+F57PSlehI6IBcQ+55ewJxnnWdhNgY82caVF
        XK3kw3PHpKaKJi1aMI9XApxXYo8DzoDE15k+YiGjabexBo5QRQPg41y7MZaPDK55hbdBMkNFm5587BGe
        35KsTEcccOMgfN2jm2tEyIhYwqMaNWgAXMLHvp7VnOpSbowp7L5fZqho08sH9/D8YoehFQNcBC8jIBax
        1oztPp/v30TYiFhA04wUVvgfogFQOI/bMoe1osd9CxdIDdWY3n75RT4hKTu7gM/uw4Y4vib7bCgUivPh
        dFxrXS9rCdYlw4KLU6i6+X8iRh/i43ERPiKaUZRkjRX4n0XBc+FVBJf+R3NgByK+tnHVj6SGakgfXTgO
        ld2DW5fZhR16v/156NsyoTxfl5LgoExcTAJjYI1Bc+kz3L9RhJGIVljbY5uk8PlyomiO4oxgD/oLuzZL
        zdSQ8Fbl1Rf3wrlDz/Dps/ZjL1/U+OPi61Eoz4eLSuD3cJkgXJwCpxfb9lC/KtXYKsJIRCusoD+xF3rN
        4GHcEChcadG698bBfDIzNUUXjj571VRMLT1c/kbP98Bdd/Dv4dq81hCbaZMnwbzpUx3HZfpYhJGIVlgh
        8znplvBRLg7nwP+xRxkXtMb/m9L5ZxeuGGL9//qx/XXHRyUmpTk+GwqF8nw43gy/Zy0WZxgZvGPzg9eO
        Oo7L9A8RRiJaYYX8gb3Q8df349df4mtI2V9v7hB37HDDY2HFct/ytEQFCeX5ju/b4fj++keW89fPHvqJ
        43Wm90QYiWiFFfJJe6E/s/kJbgY0Wl5ucDVBFFYau4muJWwc249rV0vcYoXyfPjky/ruyKHD616XjAM7
        LsJIRCuskB+zF7q9QYuVBAf89enZp+61pgrXt7If165HliySfudGFOrzYZ57V/VxrDn84N0LHcf16P7V
        IoxEtKLoxiR7oY8YMsxhlOsVXnEqyuuGZ9QJH8U292rUFIXjfLhKo/3YqmpOEGEkopU43ci0Fzp2sDVl
        RcKmCFcLwZG3uH4vro7Y4h2FLXg+nNBlHzbPRfuFxAQ4H+RP9oJvaMZdLMvdcGf6I8YuGEIiqsEZcvbC
        d2+GQ5K0cXRjjwgfEe14VHOqvfDxkanMJLGs4QOHOCoIi9ntInxEtKMo/o72wk9OzuILI8iMEovCWGBM
        7DHCAZ4ifEQMgO0QvgSppViYPdhU4Tg0e2yYqP0RayiaucVuguvdGCcatWDmDHvlYPJvFmEjYgVciMBu
        gpycwpA97o1kYQxwfok9NopuVouwEbECzpJjhf+53QgHd26UmiaW9PyOjc7KoRn/m5CQ8K8ibEQswQp/
        p90MQwYMkpomljSo/0B75QBcy1iEi4g1PF6jk90MqLvnzYHfnPuZ1DzRLBzajkuVuuOhKIFcES4iFmEm
        4NshuIUrFDZl5cNI1+4Na/k+6rIYeDTzZyJMRKzi8Rh+Zoa/uc2BwsaqzFTRpKzMzvXyLfRXVQ0kijAR
        sYxH95cyQ3zmMgiXzFTRJFmemT5VFKNYhIcgcLXFlFsVzVzqNovMVNEkd35xT/S4uIRbRFgIwonbMDJT
        RZPc+RVhIAg5bsPITBVNcudXhIEg5LgNIzNVKFTRrYrvPPXmqYa3MsDVSrqWlvMFJWTvh0Lu/IowEIQc
        t2FkpgqFrNUMcUbjD++YDyf37+T9Lyj8H19LSQmucIiVSXaMUMieV5QIA0HIcRtGZqpQCNedci/XIxPu
        R9iUDXOuV+7ziTAQhBy3YWSmCqUO79kCk8aM4Z11uCcJCjspJ40dC0f2Pin9Tijlzq8IA0HIYSb5o90w
        v3ipeSssRpJwmSN7Xpn+JsJAEHJweIXdNNYqg9Eo3Fbanlfa7pm4Jswk8+2mKS0ui8p5Ipin4sLgbr6W
        PKoxV4SBIOSoqvFdZpZLduM0d6+QSBBeGe15ZPrc603+jggDQTQMTjG1myc1NYcv0iYzmlu4Lu6YmhE3
        tH1Cc3Xmp3thzPAaqGjimrz4BM16hGzJoxkbRPYJonGC47KcI3zHjhgpNZtbuNavLz4FNG+AL53z3PYN
        0s+FQge2rYdhAwfzc+E2ak3dOGf0sBpH5WD6i6IkfU9knyCuDWuLzHaZCNYsu19qOLfwF30cq1BoXPxe
        UUEXuGvubDjy9NYbWl4Iv3to91ZYOGc2FBYENxvFc4wfOarJV6xHH1jsyBOXbswQ2SaIJlJU9GVmntN2
        I8UnpMKxZ7ZLjSfT6ed384qCVxTrGKaZwReFxp7yzatX8L6O80f2wbu2/Ujwf3wN39u06iH+WVzczmDf
        tY6DVwysGLgVm/2cjenoT7bVbdhZJ9U4gXkVuSaIpoObVjITOdbxxXt33FhGZsCGhAtK49VnYL/bICGx
        o9OgzRB+F3vf1y5fAu+8ckh6roaE+xm62x1Mn93qS24vsksQzUcV+6nbVVJU1uwdqCzhDrT7t6+DZffe
        xXvQcSOc7KzOjqsD/o+v4WBG/Ax+FtsbH50/Lj3mtYSVqbiwqyMPKI9mDhPZJIjrBycRuc2FI2zfP3tE
        asi2pPdePQw9K3s50o5SdWOJyB5B3DBfUjVjo9tk+OuPj0xlxmwLwrThcHl3uj26sR7zFMwaQYQAVVX/
        mZnrqNtsWEkam9PRWvrVyYN1Q+od0o3DPp/vayJbBBE62rfP/bqimS+4TZeR0YlvOCMzamsIn7Rhmtzp
        ZHqWKgfRogQrifGiy3iQlJTG15iSGTac2rV+DU+LO31YsTHtIhsE0XJ82zC+ydokP3WbEPs7cGem1hjc
        iOfEnnScT+JOl0cznsc0i+QTRDjo/0/MfI+6zYjq27svn2shM3JL6PVjB6C6V3W9dAitxLSKRBNEWPmS
        qht3MxNecZmS/ZIHYFZtbYteTfDYMydP5udyn5/pikc1Fop0EkTrEacZPZkh663MyG5toLxrBRx8KvTb
        KuA2BdiR6D6f+P9Tj25WieQRROuj64aHGfOkZVa7VN3ko4GbM46rIeF4KhzajseUnQvHVmFaRLIIou2Q
        np7+FbdhcTsze8MZe+CfemJVs4aN4Gd3PPEo9K7qU3ccfCBQf6s0A2jgIdGmcRsWDY4DBWey9oj98SsO
        PMTNanCPdrwFO3foGV4RUPg/voZPxNwDHJP86TBrSm3dgEnrdUsiGQTRNnEb1n4lwAGOK+67l89zd3/u
        Wiot6ca/i2Or7Md0f04kgyDaJm7D2s1sFy4nhPPCJ4wazafKpqXlge4zufB/fG3i6DGwYeXyRpcecp9P
        JIMg2iZuw8pMHUq5zyeSQRBtD0VJ1tyGlZk6lFI059MsVU1VRXIIou1wc3Lyvyiq8YbdrAmsUS4zdShV
        b4aibly8JT39GyJZBNG6KEriYEU1z7Jf8ssOozLhUyqZqUMpfMrlPi/TZZ4mljaRTIIIH3GavzMz4VHV
        tcCcXbovGd4Mw2SqX548CF6xeopMIo1HPbqRL5JPEKHnO6Z5s0cz1rJfZulmn3ZpegB+suVxqaFbQjvX
        rQLN10DPul2q8Rnm4WZaQZEIEV9mv7xzmbneZao3ILFB6SZklFbB8BlzYM2GJ+BXr1zfAg+N6c0zR+D4
        2ZOw4+evwQPnLkDt/sOQWlIlT099YV7exbxhHoNZJYgmEudN6s1uTXBNrHrtCpnik7OhZNw0uG3ZSug8
        dAx46zWcTUju1AUqR4yDsd//Ady1fDk8zirOs/v3wEuHnoWLLx+C989dHXqC/7/BKtWb50/AxYuvwNmL
        Z2D1tq0wYuZcKOk7BIysIn5ckx1z3tmLDk07dJynpUMg25mGhnUZ8xrn8/cS2SeI+rRvHyhUNRPnnH/u
        MpBUPn8GFNSMh9oDR+qZdPbp12DE+m1QPm0epHXrA3pCqvQY11JKQfe6Y8YHshzv+ZLSoXD4BMd53cK0
        YRoxrfbvNqLPMQYYCxEWIpZJSkr6nkfzb2a/oH+RmKWeNF8AsqoGwPDHNksN2ZDmnLkAE/YcgNuWr4bK
        uXdCyZhayO41EAKdy8DIKeLm1ztcverg//ha/sCRdccYtno9VH3/bhiyah0z/iGYy45pP8e1NPyxTTzt
        mAd7nhoSxgRjgzES4SJiBa83MU/RzDPMCE1oV5iQlFEAFbMXsivDOan5IklzTr3G8nIHz5M8v/V0RdX8
        r/h8STkifES0cqvuL1V04wNFlRrBIbwtKRw+HqY8V/8WKlqEt2BFw8bx2zVZDBzCmLHY3eo1ikU4iWgB
        bxNEo1te+EKaLxkyq/rDiI3bpYaKZo3YsA0yK/s37RZMNU/iY28RXiKS8fjMYR7d+EJa0CjdgEB+V6he
        vAzmNfO+PirFYlC9eCkEOnXhsZHGjIn94FxS1cRBIsxEJKLq5jZZ4aK8CR2hdMwUmHbktNwoJB6bktG1
        4E2UrbMVlEczNolwExHETapmnpIVqOoNQNdJM+lq0RyxWJVNng2qZM0toeMi7kQEcBO7pXKMqrWUXFDO
        O9KkJiBdU1MPvwSpxc6VVCyxW64LIv5EW0ZRjcP1C9CEblPnSQud1HxVzLpD2j5hleSgKAaiLeJRk37k
        LjQsyAEPPiwtaNL1a+iadfx21R1vVfXfJ4qDaEvEdTAyWQG5Ov5M6L+EKkdLqWbDNvYDVG8U8RVF8XcU
        xUK0FTya8am9oNjlHqoXPSgtWFLoNGDZo/bKIWT+QRQL0RZQNP9SdyHlVA+WFigp9MofPMoRexTdarUV
        iopw5yfHzL54IxPmvEyPccOmMxcgvv4Oup+3a9f/q6KUiNZC04zHXQUDNeu2yguS1GIau22Powy4dGOV
        KCaitWD3u/+wFwoOG5EVIKnlFSjo5qwgqvF3UUxEa6AoZqWjQJjG7tgnLTxSy2v8zmccZYFS1eRyUVxE
        uGG/UCfshYFzHGQFRwqfkrIKnRVEM46J4iLCDSsAR+O8Ys6d0kIjhU89FyxyVBAm1lgnwg6f/OQoCBNm
        nXhVWmik8Gn2y+frDUOJiwsUiGIjwoVHN9fYCyExLV9aYKTwKym9s6OC0NOsVkDVzPP2QsjtN1xaWKTw
        q9OAGkcFUTX/OVFsRLhggf+zvRB6zr9XWlik8KvnHYsdFYTpT6LYiHDh0RqZRktqU2K3w1+IYiPChawg
        SG1XotiIcMGC3qRlQUmtL49qXBbFRoQLj27crUj25CC1OV326P47RbERBEEQBEEQBEEQBEEQBEEQBEEQ
        BEEQBEEQBEEQBEEQBEEQBEEQBEEQBEEQBEEQBEEQBEEQBEEQBEEQBEEQBEEQBEEQBEEQUUi7dv8PnpSW
        oT6NrZsAAAAASUVORK5CYII=
</value>
  </data>
</root>