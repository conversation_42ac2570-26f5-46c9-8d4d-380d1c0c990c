﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Arshf
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    
    public partial class DBAREntities : DbContext
    {
        public DBAREntities()
            : base("name=DBAREntities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<TBDep> TBDeps { get; set; }
        public virtual DbSet<TBUSER> TBUSERS { get; set; }
        public virtual DbSet<TBArchiveCategory> TBArchiveCategories { get; set; }
        public virtual DbSet<TBArchiveFile> TBArchiveFiles { get; set; }
    }
}
