﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Arshf.Pages
{
    public partial class DepPage : DevExpress.XtraEditors.XtraUserControl
    {
        // DataBase and Tables
        private DBAREntities db;
        TBDep tbadd;
        // other var
        int id;
        private bool state;

        public DepPage()
        {
            InitializeComponent();
            LoadData();
        }

        // Load Data
        public void LoadData()
        {
            // This line of code is generated by Data Source Configuration Wizard
            this.entityInstantFeedbackSource1.GetQueryable += entityInstantFeedbackSource1_GetQueryable;
            // This line of code is generated by Data Source Configuration Wizard
            this.entityInstantFeedbackSource1.DismissQueryable += entityInstantFeedbackSource1_DismissQueryable;
            this.entityInstantFeedbackSource1.Refresh();
        }
        // This event is generated by Data Source Configuration Wizard
        void entityInstantFeedbackSource1_GetQueryable(object sender, DevExpress.Data.Linq.GetQueryableEventArgs e)
        {
            // Instantiate a new DataContext
            Arshf.DBAREntities dataContext = new Arshf.DBAREntities();
            // Assign a queryable source to the EntityInstantFeedbackSource
            e.QueryableSource = dataContext.TBDeps;
            // Assign the DataContext to the Tag property,
            // to dispose of it in the DismissQueryable event handler
            e.Tag = dataContext;
        }

        // This event is generated by Data Source Configuration Wizard
        void entityInstantFeedbackSource1_DismissQueryable(object sender, DevExpress.Data.Linq.GetQueryableEventArgs e)
        {
            // Dispose of the DataContext
            ((Arshf.DBAREntities)e.Tag).Dispose();
        }

        // Add
        private void btn_add_Click(object sender, EventArgs e)
        {
            AddPage.AddDep add = new AddPage.AddDep();
            add.btn_add.Text = "اضافة";
            add.id = 0;
            add.page = this;
            add.Show();
        }

        // Edit
        private void btn_edit_Click(object sender, EventArgs e)
        {
            try
            {
                id = Convert.ToInt32(gridView1.GetFocusedRowCellValue("ID"));
                if (id > 0)
                {
                    // Edit
                    AddPage.AddDep add = new AddPage.AddDep();
                    add.btn_add.Text = "تعديل";
                    add.id = id;
                    add.edt_name.Text = Convert.ToString(gridView1.GetFocusedRowCellValue("DepName"));
                    add.edt_details.Text = Convert.ToString(gridView1.GetFocusedRowCellValue("DepDetails"));
                    add.page = this;
                    add.Show();
                }
                else
                {
                    MessageBox.Show("لا بيانات لتعديلها");
                }
            }
            catch(Exception ex)
            {
                MessageBox.Show("خطأ في العملية", ex.Message, MessageBoxButtons.OK, MessageBoxIcon.Error);

            }


        }

        // Delete
        private async void btn_delete_Click(object sender, EventArgs e)
        {
            try
            {
                id = Convert.ToInt32(gridView1.GetFocusedRowCellValue("ID"));
                if (id > 0)
                {
                    var result = MessageBox.Show("اجراء حذف", "هل انت متأكد من هذا الاجراء", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    if (result == DialogResult.Yes)
                    {
                        // Delete
                        // Loading
                        loading.Visible = true;
                        tbadd = new TBDep
                        {
                            ID = id,
                        };
                        var rs = await Task.Run(() => Delete(tbadd));
                        if (rs == true)
                        {
                            LoadData();
                            toastNotificationsManager1.ShowNotification("96616aac-b9fe-45d7-8bb1-5cd04f388f4c");

                        }
                        else
                        {
                            MessageBox.Show("خطأ في الاتصال", "تأكد من الاتصال في السيرفر لطفا", MessageBoxButtons.OK, MessageBoxIcon.Error);

                        }
                        //End 
                        loading.Visible = false;
                    }

                   
                  
                }
                else
                {
                    MessageBox.Show("لا بيانات لحذفها");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في العملية", ex.Message, MessageBoxButtons.OK, MessageBoxIcon.Error);

            }

        }

        // Delete Method
        private bool Delete(TBDep Data)
        {
            try
            {
                db = new DBAREntities();
                db.Entry(Data).State = System.Data.Entity.EntityState.Deleted;
                db.SaveChanges();
                state = true;

            }
            catch { state = false; }
            return state;
        }

        // Print 
        private void btn_print_Click(object sender, EventArgs e)
        {
            gridControl1.ShowPrintPreview();
        }

        private void DepPage_Leave(object sender, EventArgs e)
        {
        }
    }
}
