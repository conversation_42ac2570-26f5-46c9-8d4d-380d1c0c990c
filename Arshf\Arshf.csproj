<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{7E9D3BDC-0190-4B50-90E5-F0B7778A18C3}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Arshf</RootNamespace>
    <AssemblyName>Arshf</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>icon arshf project.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AddPage\AddArchiveCategory.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AddPage\AddArchiveCategory.Designer.cs">
      <DependentUpon>AddArchiveCategory.cs</DependentUpon>
    </Compile>
    <Compile Include="AddPage\AddFile.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AddPage\AddFile.Designer.cs">
      <DependentUpon>AddFile.cs</DependentUpon>
    </Compile>
    <Compile Include="AddPage\AddNewFileFromHome.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AddPage\AddNewFileFromHome.Designer.cs">
      <DependentUpon>AddNewFileFromHome.cs</DependentUpon>
    </Compile>
    <Compile Include="AddPage\LoginFrom.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AddPage\LoginFrom.Designer.cs">
      <DependentUpon>LoginFrom.cs</DependentUpon>
    </Compile>
    <Compile Include="AddPage\AddUser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AddPage\AddUser.Designer.cs">
      <DependentUpon>AddUser.cs</DependentUpon>
    </Compile>
    <Compile Include="AddPage\AddDep.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AddPage\AddDep.Designer.cs">
      <DependentUpon>AddDep.cs</DependentUpon>
    </Compile>
    <Compile Include="DBAR.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DBAR.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="DBAR.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DBAR.tt</DependentUpon>
    </Compile>
    <Compile Include="DBAR.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DBAR.edmx</DependentUpon>
    </Compile>
    <Compile Include="Main.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main.Designer.cs">
      <DependentUpon>Main.cs</DependentUpon>
    </Compile>
    <Compile Include="Pages\ArchiveCatPage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Pages\ArchiveCatPage.Designer.cs">
      <DependentUpon>ArchiveCatPage.cs</DependentUpon>
    </Compile>
    <Compile Include="Pages\UsersPage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Pages\UsersPage.Designer.cs">
      <DependentUpon>UsersPage.cs</DependentUpon>
    </Compile>
    <Compile Include="Pages\DepPage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Pages\DepPage.Designer.cs">
      <DependentUpon>DepPage.cs</DependentUpon>
    </Compile>
    <Compile Include="Pages\HomePage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Pages\HomePage.Designer.cs">
      <DependentUpon>HomePage.cs</DependentUpon>
    </Compile>
    <Compile Include="PL\SearchResults.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PL\SearchResults.Designer.cs">
      <DependentUpon>SearchResults.cs</DependentUpon>
    </Compile>
    <Compile Include="PL\SettingForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PL\SettingForm.Designer.cs">
      <DependentUpon>SettingForm.cs</DependentUpon>
    </Compile>
    <Compile Include="PL\ShowFiles.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PL\ShowFiles.Designer.cs">
      <DependentUpon>ShowFiles.cs</DependentUpon>
    </Compile>
    <Compile Include="PL\ArchiveFileForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PL\ArchiveFileForm.Designer.cs">
      <DependentUpon>ArchiveFileForm.cs</DependentUpon>
    </Compile>
    <Compile Include="PL\Start.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PL\Start.Designer.cs">
      <DependentUpon>Start.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="TBArchiveCategory.cs">
      <DependentUpon>DBAR.tt</DependentUpon>
    </Compile>
    <Compile Include="TBArchiveFile.cs">
      <DependentUpon>DBAR.tt</DependentUpon>
    </Compile>
    <Compile Include="TBDep.cs">
      <DependentUpon>DBAR.tt</DependentUpon>
    </Compile>
    <Compile Include="TBUSER.cs">
      <DependentUpon>DBAR.tt</DependentUpon>
    </Compile>


    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="App.config" />
    <EntityDeploy Include="DBAR.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>DBAR.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <None Include="DBAR.edmx.diagram">
      <DependentUpon>DBAR.edmx</DependentUpon>
    </None>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="BL\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="DBAR.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>DBAR.Context.cs</LastGenOutput>
      <DependentUpon>DBAR.edmx</DependentUpon>
    </Content>
    <Content Include="DBAR.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>DBAR.edmx</DependentUpon>
      <LastGenOutput>DBAR.cs</LastGenOutput>
    </Content>
    <Content Include="icon arshf project.ico" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>