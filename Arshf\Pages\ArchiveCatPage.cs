﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Arshf.Pages
{
    public partial class ArchiveCatPage : System.Windows.Forms.UserControl
    {
        // DataBase and Tables
        private DBAREntities db;
        TBArchiveCategory tbadd;
        // other var
        int id;
        private bool state;
        private string UserRole;
        private string UserDep;

        public ArchiveCatPage()
        {
            InitializeComponent();
            UserRole = Properties.Settings.Default.UserRole;
            UserDep = Properties.Settings.Default.UserDep;
            LoadData();
        }

        // Load Data
        public void LoadData()
        {
            try
            {
                db = new DBAREntities();
                var categories = UserRole == "مدير"
                    ? db.TBArchiveCategories.Select(x => new { x.ID, x.ArchiveTitle, x.ArchiveDetails, x.UserDep }).ToList()
                    : db.TBArchiveCategories.Where(x => x.UserDep == UserDep).Select(x => new { x.ID, x.ArchiveTitle, x.ArchiveDetails, x.UserDep }).ToList();

                dataGridView1.DataSource = categories;

                if (dataGridView1.Columns.Count > 0)
                {
                    dataGridView1.Columns["ID"].HeaderText = "الرقم";
                    dataGridView1.Columns["ArchiveTitle"].HeaderText = "عنوان التصنيف";
                    dataGridView1.Columns["ArchiveDetails"].HeaderText = "تفاصيل التصنيف";
                    dataGridView1.Columns["UserDep"].HeaderText = "القسم";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل البيانات: " + ex.Message);
            }
        }

        private void btn_add_Click(object sender, EventArgs e)
        {
            AddPage.AddArchiveCategory addForm = new AddPage.AddArchiveCategory();
            addForm.page = this;
            addForm.ShowDialog();
        }

        private void btn_edit_Click(object sender, EventArgs e)
        {
            if (dataGridView1.SelectedRows.Count > 0)
            {
                int id = Convert.ToInt32(dataGridView1.SelectedRows[0].Cells["ID"].Value);
                AddPage.AddArchiveCategory editForm = new AddPage.AddArchiveCategory();
                editForm.id = id;
                editForm.page = this;
                editForm.ShowDialog();
            }
            else
            {
                MessageBox.Show("يرجى اختيار تصنيف للتعديل");
            }
        }

        private void btn_delete_Click(object sender, EventArgs e)
        {
            if (dataGridView1.SelectedRows.Count > 0)
            {
                if (MessageBox.Show("هل أنت متأكد من حذف هذا التصنيف؟", "تأكيد الحذف", MessageBoxButtons.YesNo) == DialogResult.Yes)
                {
                    try
                    {
                        int id = Convert.ToInt32(dataGridView1.SelectedRows[0].Cells["ID"].Value);
                        var category = db.TBArchiveCategories.Find(id);
                        if (category != null)
                        {
                            db.TBArchiveCategories.Remove(category);
                            db.SaveChanges();
                            LoadData();
                            MessageBox.Show("تم حذف التصنيف بنجاح");
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("خطأ في حذف التصنيف: " + ex.Message);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار تصنيف للحذف");
            }
        }

        private void btn_showfiles_Click(object sender, EventArgs e)
        {
            if (dataGridView1.SelectedRows.Count > 0)
            {
                int id = Convert.ToInt32(dataGridView1.SelectedRows[0].Cells["ID"].Value);
                PL.ArchiveFileForm fileForm = new PL.ArchiveFileForm();
                fileForm.ArchiveID = id;
                fileForm.Show();
            }
            else
            {
                MessageBox.Show("يرجى اختيار تصنيف لعرض الملفات");
            }
        }
        // This event is generated by Data Source Configuration Wizard
        void entityInstantFeedbackSource1_GetQueryable(object sender, DevExpress.Data.Linq.GetQueryableEventArgs e)
        {
            // Instantiate a new DataContext
            Arshf.DBAREntities dataContext = new Arshf.DBAREntities();
            // User Role = مدير
            if (UserRole == "مدير")
            {
                e.QueryableSource = dataContext.TBArchiveCategories;

            }
            else
            {
                e.QueryableSource = dataContext.TBArchiveCategories.Where(x=>x.UserDep==UserDep);

            }
            // to dispose of it in the DismissQueryable event handler
            e.Tag = dataContext;
        }

        // This event is generated by Data Source Configuration Wizard
        void entityInstantFeedbackSource1_DismissQueryable(object sender, DevExpress.Data.Linq.GetQueryableEventArgs e)
        {
            // Dispose of the DataContext
            ((Arshf.DBAREntities)e.Tag).Dispose();
        }

        // Add
        private void btn_add_Click(object sender, EventArgs e)
        {
            AddPage.AddArchiveCategory add = new AddPage.AddArchiveCategory();
            add.btn_add.Text = "اضافة";
            add.id = 0;
            add.page = this;
            add.Show();
        }

        // Edit
        private void btn_edit_Click(object sender, EventArgs e)
        {
            try
            {
                id = Convert.ToInt32(tileView1.GetFocusedRowCellValue("ID"));
                if (id > 0)
                {
                    // Edit
                    AddPage.AddArchiveCategory add = new AddPage.AddArchiveCategory();
                    add.btn_add.Text = "تعديل";
                    add.id = id;
                    add.edt_name.Text = Convert.ToString(tileView1.GetFocusedRowCellValue("ArchiveTitle"));
                    add.edt_details.Text = Convert.ToString(tileView1.GetFocusedRowCellValue("ArchiveDes"));
                    add.page = this;
                    add.Show();
                }
                else
                {
                    MessageBox.Show("لا بيانات لتعديلها");
                }
            }
            catch(Exception ex)
            {
                MessageBox.Show("خطأ في العملية", ex.Message, MessageBoxButtons.OK, MessageBoxIcon.Error);

            }


        }

        // Delete
        private async void btn_delete_Click(object sender, EventArgs e)
        {
            try
            {
                id = Convert.ToInt32(tileView1.GetFocusedRowCellValue("ID"));
                if (id > 0)
                {
                    var result = MessageBox.Show("اجراء حذف", "هل انت متأكد من هذا الاجراء", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    if (result == DialogResult.Yes)
                    {
                        // Delete
                        // Loading
                        loading.Visible = true;
                        tbadd = new TBArchiveCategory
                        {
                            ID = id,
                        };
                        var rs = await Task.Run(() => Delete(tbadd));
                        if (rs == true)
                        {
                            LoadData();
                            toastNotificationsManager1.ShowNotification("96616aac-b9fe-45d7-8bb1-5cd04f388f4c");

                        }
                        else
                        {
                            MessageBox.Show("خطأ في الاتصال", "تأكد من الاتصال في السيرفر لطفا", MessageBoxButtons.OK, MessageBoxIcon.Error);

                        }
                        //End 
                        loading.Visible = false;
                    }

                   
                  
                }
                else
                {
                    MessageBox.Show("لا بيانات لحذفها");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في العملية", ex.Message, MessageBoxButtons.OK, MessageBoxIcon.Error);

            }

        }

        // Delete Method
        private bool Delete(TBArchiveCategory Data)
        {
            try
            {
                db = new DBAREntities();
                db.Entry(Data).State = System.Data.Entity.EntityState.Deleted;
                db.SaveChanges();
                state = true;

            }
            catch { state = false; }
            return state;
        }

        // Print 
        private void btn_print_Click(object sender, EventArgs e)
        {
            gridControl1.ShowPrintPreview();
        }

        private void tileView1_ItemDoubleClick(object sender, DevExpress.XtraGrid.Views.Tile.TileViewItemClickEventArgs e)
        {
            try
            {
                PL.ArchiveFileForm archive = new PL.ArchiveFileForm();
                id = Convert.ToInt32(tileView1.GetFocusedRowCellValue("ID"));
                archive.lb_id.Text = id.ToString();
                archive.ArchiveID = id;
                archive.lb_archivename.Text = Convert.ToString(tileView1.GetFocusedRowCellValue("ArchiveTitle"));
                archive.lb_archdep.Text = Convert.ToString(tileView1.GetFocusedRowCellValue("UserDep"));
                archive.Show();

                
            }
            catch { }
        }
    }
}
