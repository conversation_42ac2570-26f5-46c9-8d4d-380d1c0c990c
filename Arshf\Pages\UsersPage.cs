using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Arshf.Pages
{
    public partial class UsersPage : System.Windows.Forms.UserControl
    {
        // DataBase and Tables
        private DBAREntities db;
        TBUSER tbadd;
        // other var
        int id;
        private bool state;

        public UsersPage()
        {
            db = new DBAREntities();
            InitializeComponent();
            LoadData();
         
        }

        // Load Data
        public void LoadData()
        {
            // This line of code is generated by Data Source Configuration Wizard
            this.entityInstantFeedbackSource2.GetQueryable += entityInstantFeedbackSource2_GetQueryable;
            // This line of code is generated by Data Source Configuration Wizard
            this.entityInstantFeedbackSource2.DismissQueryable += entityInstantFeedbackSource2_DismissQueryable;
            this.entityInstantFeedbackSource2.Refresh();
        }
       

        // Add
        private void btn_add_Click(object sender, EventArgs e)
        {
            AddPage.AddUser add = new AddPage.AddUser();
            add.btn_add.Text = "اضافة";
            add.id = 0;
            add.page = this;
            add.Show();
        }

        // Edit
        private void btn_edit_Click(object sender, EventArgs e)
        {
            try
            {
                id = Convert.ToInt32(gridView1.GetFocusedRowCellValue("ID"));
                if (id > 0)
                {
                    // Edit
                    AddPage.AddUser add = new AddPage.AddUser();
                    add.btn_add.Text = "تعديل";
                    add.id = id;
                    add.depid = Convert.ToInt32(gridView1.GetFocusedRowCellValue("IDDep"));
                    add.edt_name.Text = Convert.ToString(gridView1.GetFocusedRowCellValue("FullName"));
                    add.edt_username.Text = Convert.ToString(gridView1.GetFocusedRowCellValue("UserName"));
                    add.edt_password.Text = Convert.ToString(gridView1.GetFocusedRowCellValue("Password"));
                    add.edt_role.Text = Convert.ToString(gridView1.GetFocusedRowCellValue("UserRole"));
                    add.edt_dep.Text = Convert.ToString(gridView1.GetFocusedRowCellValue("DepName"));
                    add.page = this;
                    add.Show();
                }
                else
                {
                    MessageBox.Show("لا بيانات لتعديلها");
                }
            }
            catch(Exception ex)
            {
                MessageBox.Show("خطأ في العملية", ex.Message, MessageBoxButtons.OK, MessageBoxIcon.Error);

            }


        }

        // Delete
        private async void btn_delete_Click(object sender, EventArgs e)
        {
            try
            {
                id = Convert.ToInt32(gridView1.GetFocusedRowCellValue("ID"));
                if (id > 0)
                {
                    var result = MessageBox.Show("اجراء حذف", "هل انت متأكد من هذا الاجراء", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    if (result == DialogResult.Yes)
                    {
                        // Delete
                        // Loading
                        loading.Visible = true;
                        tbadd = new TBUSER
                        {
                            ID = id,
                        };
                        var rs = await Task.Run(() => Delete(tbadd));
                        if (rs == true)
                        {
                            LoadData();
                            toastNotificationsManager1.ShowNotification("96616aac-b9fe-45d7-8bb1-5cd04f388f4c");

                        }
                        else
                        {
                            MessageBox.Show("خطأ في الاتصال", "تأكد من الاتصال في السيرفر لطفا", MessageBoxButtons.OK, MessageBoxIcon.Error);

                        }
                        //End 
                        loading.Visible = false;
                    }

                   
                  
                }
                else
                {
                    MessageBox.Show("لا بيانات لحذفها");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في العملية", ex.Message, MessageBoxButtons.OK, MessageBoxIcon.Error);

            }

        }

        // Delete Method
        private bool Delete(TBUSER Data)
        {
            try
            {
                db.Entry(Data).State = System.Data.Entity.EntityState.Deleted;
                db.SaveChanges();
                state = true;

            }
            catch  {  state = false; }
            return state;
        }

        // Print 
        private void btn_print_Click(object sender, EventArgs e)
        {
            gridControl1.ShowPrintPreview();
        }

        // This event is generated by Data Source Configuration Wizard
        void entityInstantFeedbackSource2_GetQueryable(object sender, DevExpress.Data.Linq.GetQueryableEventArgs e)
        {
            // Instantiate a new DataContext
            Arshf.DBAREntities dataContext = new Arshf.DBAREntities();
            // Assign a queryable source to the EntityInstantFeedbackSource
            e.QueryableSource = dataContext.TBUSERS;
            // Assign the DataContext to the Tag property,
            // to dispose of it in the DismissQueryable event handler
            e.Tag = dataContext;
        }

        // This event is generated by Data Source Configuration Wizard
        void entityInstantFeedbackSource2_DismissQueryable(object sender, DevExpress.Data.Linq.GetQueryableEventArgs e)
        {
            // Dispose of the DataContext
            ((Arshf.DBAREntities)e.Tag).Dispose();
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            Properties.Settings.Default.UserID = Convert.ToInt32(gridView1.GetFocusedRowCellValue("ID"));

            Properties.Settings.Default.FullName = Convert.ToString(gridView1.GetFocusedRowCellValue("FullName"));
            Properties.Settings.Default.UserName = Convert.ToString(gridView1.GetFocusedRowCellValue("UserName"));
            Properties.Settings.Default.UserRole = Convert.ToString(gridView1.GetFocusedRowCellValue("UserRole"));
            Properties.Settings.Default.UserDep = Convert.ToString(gridView1.GetFocusedRowCellValue("DepName"));
            Properties.Settings.Default.Save();
        }
    }
}
