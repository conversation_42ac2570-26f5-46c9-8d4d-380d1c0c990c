﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Arshf.PL
{
    public partial class SearchResults : DevExpress.XtraEditors.XtraForm
    {
        // Data Base and Tables
        TBArchiveFile tbadd;
        DBAREntities db;
        // other var
        public int ArchiveID;
        private int id;
        private bool state;
        public string SeachText;
        string DepName;
        string UserRole;

        public SearchResults()
        {
            InitializeComponent();
            DepName = Properties.Settings.Default.UserDep;
            UserRole = Properties.Settings.Default.UserRole;
          
           
                LoadData();
            


        }

        public void LoadData()
        {
            // This line of code is generated by Data Source Configuration Wizard
            this.entityInstantFeedbackSource1.GetQueryable += entityInstantFeedbackSource1_GetQueryable;
            // This line of code is generated by Data Source Configuration Wizard
            this.entityInstantFeedbackSource1.DismissQueryable += entityInstantFeedbackSource1_DismissQueryable;
            this.entityInstantFeedbackSource1.Refresh();
        }
       
        // This event is generated by Data Source Configuration Wizard
        void entityInstantFeedbackSource1_GetQueryable(object sender, DevExpress.Data.Linq.GetQueryableEventArgs e)
        {
            // Instantiate a new DataContext
            Arshf.DBAREntities dataContext = new Arshf.DBAREntities();
            // Assign a queryable source to the EntityInstantFeedbackSource
            if (UserRole == "مدير")
            {
                e.QueryableSource = dataContext.TBArchiveFiles.Where(x => x.ArchNumber.Contains(SeachText) || x.ArchTitle.Contains(SeachText) || x.ArchReciver.Contains(SeachText) || x.ArchSender.Contains(SeachText))
               .Select(x => new
               {
                   x.ID,
                   x.IDArchive,
                   x.AddDate,
                   x.ArchDate,
                   x.ArchDep,
                   x.ArchDetails,
                   x.ArchNumber,
                   x.ArchReciver,
                   x.ArchSender,
                   x.ArchTitle
               });
                // to dispose of it in the DismissQueryable event handler
                e.Tag = dataContext;
            }
            else
            {
                e.QueryableSource = dataContext.TBArchiveFiles.Where(x=>x.ArchDep==DepName).Where(x => x.ArchNumber.Contains(SeachText) || x.ArchTitle.Contains(SeachText) || x.ArchReciver.Contains(SeachText) || x.ArchSender.Contains(SeachText))
             .Select(x => new
             {
                 x.ID,
                 x.IDArchive,
                 x.AddDate,
                 x.ArchDate,
                 x.ArchDep,
                 x.ArchDetails,
                 x.ArchNumber,
                 x.ArchReciver,
                 x.ArchSender,
                 x.ArchTitle
             });
                // to dispose of it in the DismissQueryable event handler
                e.Tag = dataContext;
            }
           
        }

        // This event is generated by Data Source Configuration Wizard
        void entityInstantFeedbackSource1_DismissQueryable(object sender, DevExpress.Data.Linq.GetQueryableEventArgs e)
        {
            // Dispose of the DataContext
            ((Arshf.DBAREntities)e.Tag).Dispose();
        }


        private void btn_add_Click(object sender, EventArgs e)
        {
          
        }

        private void btn_edit_Click(object sender, EventArgs e)
        {
           
        }

        private async void btn_delete_Click(object sender, EventArgs e)
        {
            try
            {
                id = Convert.ToInt32(gridView1.GetFocusedRowCellValue("ID"));
                if (id > 0)
                {
                    var result = MessageBox.Show("اجراء حذف", "هل انت متأكد من هذا الاجراء", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    if (result == DialogResult.Yes)
                    {
                        // Delete
                        // Loading
                        loading.Visible = true;
                        tbadd = new TBArchiveFile
                        {
                            ID = id,
                        };
                        var rs = await Task.Run(() => Delete(tbadd));
                        if (rs == true)
                        {
                            LoadData();
                            toastNotificationsManager1.ShowNotification("96616aac-b9fe-45d7-8bb1-5cd04f388f4c");

                        }
                        else
                        {
                            MessageBox.Show("خطأ في الاتصال", "تأكد من الاتصال في السيرفر لطفا", MessageBoxButtons.OK, MessageBoxIcon.Error);

                        }
                        //End 
                        loading.Visible = false;
                    }



                }
                else
                {
                    MessageBox.Show("لا بيانات لحذفها");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في العملية", ex.Message, MessageBoxButtons.OK, MessageBoxIcon.Error);

            }
        }
        // Delete Method
        private bool Delete(TBArchiveFile Data)
        {
            try
            {
                db = new DBAREntities();
                db.Entry(Data).State = System.Data.Entity.EntityState.Deleted;
                db.SaveChanges();
                state = true;

            }
            catch { state = false; }
            return state;
        }

        private void btn_print_Click(object sender, EventArgs e)
        {
            gridControl1.ShowPrintPreview();
        }

        private void btn_showfiles_Click(object sender, EventArgs e)
        {
            try
            {
                id = Convert.ToInt32(gridView1.GetFocusedRowCellValue("ID"));
                if (id > 0)
                {


                    PL.ShowFiles files = new ShowFiles();
                    files.id = id;
                    files.Show();


                }
                else
                {
                    MessageBox.Show("لا بيانات ");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في العملية", ex.Message, MessageBoxButtons.OK, MessageBoxIcon.Error);

            }
           
        }
    }
}