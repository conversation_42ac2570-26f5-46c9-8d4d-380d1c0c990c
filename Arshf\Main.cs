﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace Arshf
{
    public partial class Main : System.Windows.Forms.Form
    {
        public Main()
        {
            InitializeComponent();
            LoadHomePage();
        }


        // Load Home Page
        private void btn_home_Click(object sender, EventArgs e)
        {
            LoadHomePage();
        }

        private void LoadHomePage()
        {
            Pages.HomePage page = new Pages.HomePage();
            LoadPage(page);
        }

        // Load Page Method
        private void LoadPage(System.Windows.Forms.UserControl Page)
        {
            try
            {
                var oldpage = pn_container.Controls.OfType<UserControl>().FirstOrDefault();
                if (oldpage != null)
                {
                    pn_container.Controls.Remove(oldpage);
                    oldpage.Dispose();
                }


                Page.Dock = DockStyle.Fill;
                pn_container.Controls.Add(Page);
            }
            catch { }
        }

        private void btn_dep_Click(object sender, EventArgs e)
        {
            Pages.DepPage page = new Pages.DepPage();
            LoadPage(page);
        }

        private void btn_users_Click(object sender, EventArgs e)
        {
            Pages.UsersPage page = new Pages.UsersPage();
            LoadPage(page);
        }

        private void btn_archive_Click(object sender, EventArgs e)
        {
            Pages.ArchiveCatPage page = new Pages.ArchiveCatPage();
            LoadPage(page);
        }

        private void btn_settings_Click(object sender, EventArgs e)
        {
            PL.SettingForm setting = new PL.SettingForm();
            setting.Show();
        }

        private void btn_help_Click(object sender, EventArgs e)
        {
            AddPage.LoginFrom loginFrom = new AddPage.LoginFrom();
            loginFrom.Show();
            Hide();
        }

        private void Main_FormClosed(object sender, FormClosedEventArgs e)
        {
            Application.Exit();
        }
    }
}
